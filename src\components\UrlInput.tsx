import React, { useState } from 'react'
import { Globe, Loader2, AlertCircle, Lock } from 'lucide-react'
import { Document } from '../types'
import { extractWebContent, createWebDocument, isValidUrl } from '../services/webExtractionService'
import { saveDocument } from '../utils/documentUtils'

export const UrlInput: React.FC<{
  onExtractComplete: (document: Document) => void
  className?: string
}> = ({ onExtractComplete, className = '' }) => {
  const [url, setUrl] = useState('')
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [showAuth, setShowAuth] = useState(false)
  const [isExtracting, setIsExtracting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [validationError, setValidationError] = useState<string | null>(null)

  const validateUrl = (inputUrl: string) => {
    if (!inputUrl.trim()) {
      setValidationError(null)
      return false
    }
    
    if (!isValidUrl(inputUrl)) {
      setValidationError('Please enter a valid HTTP or HTTPS URL')
      return false
    }
    
    setValidationError(null)
    return true
  }

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value
    setUrl(newUrl)
    setError(null)
    
    // Validate URL on change
    if (newUrl.trim()) {
      validateUrl(newUrl)
    } else {
      setValidationError(null)
    }
  }



  const handleExtract = async () => {
    if (!validateUrl(url)) {
      return
    }

    setIsExtracting(true)
    setError(null)

    try {
      const extractionOptions = {
        maxContentLength: 50000,
        timeout: 60000, // Increased to 60 seconds to allow for retry logic
        auth: showAuth && username && password ? {
          username: username.trim(),
          password: password.trim()
        } : undefined
      }
      
      const result = await extractWebContent(url.trim(), extractionOptions)

      if (result.success && result.content) {
        // Create document from extracted content
        const webDocument = createWebDocument(result)
        
        // Save to storage
        await saveDocument(webDocument)
        
        // Notify parent component
        onExtractComplete(webDocument)
        
        // Reset form
        setUrl('')
        setUsername('')
        setPassword('')
        setShowAuth(false)
        setError(null)
      } else {
        setError(result.error || 'Failed to extract content from the URL')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setIsExtracting(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleExtract()
  }

  const isValidInput = url.trim() && !validationError && !isExtracting

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="url-input" className="block text-sm font-medium text-gray-700 mb-2">
            URL
          </label>
          <input
            id="url-input"
            type="url"
            value={url}
            onChange={handleUrlChange}
            placeholder="https://example.com/article"
            disabled={isExtracting}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed ${
              validationError ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
            }`}
          />
          
          {validationError && (
            <div className="flex items-center gap-1 mt-1 text-sm text-red-600">
              <AlertCircle className="w-4 h-4" />
              <span>{validationError}</span>
            </div>
          )}
        </div>

        {/* Authentication Section */}
        <div>
          <button
            type="button"
            onClick={() => setShowAuth(!showAuth)}
            className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
          >
            <Lock className="w-4 h-4" />
            <span>{showAuth ? 'Hide' : 'Show'} Authentication</span>
          </button>
          
          {showAuth && (
            <div className="mt-3 p-3 bg-gray-50 rounded-md border border-gray-200">
              <div className="text-xs text-gray-600 mb-2">For pages requiring basic authentication</div>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label htmlFor="username-input" className="block text-xs font-medium text-gray-700 mb-1">
                    Username
                  </label>
                  <input
                    id="username-input"
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder="Username"
                    disabled={isExtracting}
                    className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50"
                  />
                </div>
                <div>
                  <label htmlFor="password-input" className="block text-xs font-medium text-gray-700 mb-1">
                    Password
                  </label>
                  <input
                    id="password-input"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Password"
                    disabled={isExtracting}
                    className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50"
                  />
                </div>
              </div>
            </div>
          )}
        </div>



        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <AlertCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-red-700">
                <p className="font-medium">Extraction Failed</p>
                <p className="mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        <button
          type="submit"
          disabled={!isValidInput}
          className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isExtracting ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Extracting...</span>
            </>
          ) : (
            <>
              <Globe className="w-4 h-4" />
              <span>Extract Content</span>
            </>
          )}
        </button>
      </form>

      {isExtracting && (
        <div className="mt-4 text-sm text-gray-600">
          <p>Fetching and processing content from the website...</p>
          <p className="text-xs text-gray-500 mt-1">
            This may take up to a minute depending on the page size and server response time.
            The system will automatically retry if the initial request times out.
          </p>
        </div>
      )}
    </div>
  )
}

export default UrlInput