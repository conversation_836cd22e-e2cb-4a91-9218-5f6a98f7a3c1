import React, { useState, useRef } from 'react'
import { X, Upload, FileText, Trash2, Check, Globe, Edit2 } from 'lucide-react'
import { Document } from '@/types'
import { uploadDocument, deleteDocument, renameDocument } from '@/utils/documentUtils'
import { validateFile } from '@/utils/fileUtils'
import { isEmbeddingApiConfigured } from '@/services/embeddingService'
import { toast } from 'sonner'
import UrlInput from './UrlInput'
import DocumentRenameInput from './DocumentRenameInput'

interface DocumentManagerModalProps {
  isOpen: boolean
  onClose: () => void
  documents: Document[]
  selectedDocumentIds: string[]
  onSelectionChange: (documentIds: string[]) => void
  onDocumentsUpdate: () => void
}

export default function DocumentManagerModal({
  isOpen,
  onClose,
  documents,
  selectedDocumentIds,
  onSelectionChange,
  onDocumentsUpdate
}: DocumentManagerModalProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [dragActive, setDragActive] = useState(false)
  const [activeTab, setActiveTab] = useState<'files' | 'url'>('files')
  const [renamingDocumentId, setRenamingDocumentId] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  if (!isOpen) return null

  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return
    
    const fileArray = Array.from(files)
    handleFileUpload(fileArray)
  }

  const handleFileUpload = async (files: File[]) => {
    setIsUploading(true)
    setUploadProgress(0)

    try {
      const totalFiles = files.length
      let completedFiles = 0
      let successCount = 0
      let failureCount = 0

      for (const file of files) {
        // Validate file using the centralized validation function
        const validation = validateFile(file)
        if (!validation.isValid) {
          toast.error(`${validation.error}: ${file.name}`)
          failureCount++
          continue
        }

        try {
          // Show progress for current file
          toast.info(`Processing ${file.name}...`, { duration: 2000 })
          
          await uploadDocument(file)
          completedFiles++
          successCount++
          setUploadProgress((completedFiles / totalFiles) * 100)
          
          // Check if embeddings were generated successfully
          const apiConfig = isEmbeddingApiConfigured()
          
          if (!apiConfig.configured) {
            toast.success(`✅ ${file.name} uploaded (using mock embeddings - ${apiConfig.message})`, { duration: 5000 })
          } else {
            toast.success(`✅ ${file.name} uploaded and processed successfully`, { duration: 3000 })
          }
        } catch (error) {
          console.error('Upload error:', error)
          failureCount++
          
          let errorMessage = 'Unknown error'
          if (error instanceof Error) {
            if (error.message.includes('timeout')) {
              errorMessage = 'Upload timeout - file may be too large or network is slow'
            } else if (error.message.includes('API')) {
              errorMessage = 'API configuration issue - check your .env file'
            } else if (error.message.includes('quota')) {
              errorMessage = 'Storage quota exceeded - try clearing old documents'
            } else {
              errorMessage = error.message
            }
          }
          
          toast.error(`❌ Failed to upload ${file.name}: ${errorMessage}`, { duration: 5000 })
        }
      }
      
      // Show final summary
      if (totalFiles > 1) {
        if (successCount === totalFiles) {
          toast.success(`🎉 All ${totalFiles} files uploaded successfully!`, { duration: 4000 })
        } else if (successCount > 0) {
          toast.info(`📊 Upload complete: ${successCount} succeeded, ${failureCount} failed`, { duration: 5000 })
        } else {
          toast.error(`❌ All uploads failed. Please check your files and configuration.`, { duration: 5000 })
        }
      }

      // Refresh documents list
      onDocumentsUpdate()
    } catch (error) {
      console.error('Upload process error:', error)
      toast.error('Upload process failed')
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files)
    }
  }

  const handleDocumentToggle = (documentId: string) => {
    const isSelected = selectedDocumentIds.includes(documentId)
    if (isSelected) {
      onSelectionChange(selectedDocumentIds.filter(id => id !== documentId))
    } else {
      onSelectionChange([...selectedDocumentIds, documentId])
    }
  }

  const handleSelectAll = () => {
    if (selectedDocumentIds.length === documents.length) {
      onSelectionChange([])
    } else {
      onSelectionChange(documents.map(doc => doc.id))
    }
  }

  const handleDeleteDocument = async (documentId: string) => {
    if (confirm('Are you sure you want to delete this document?')) {
      try {
        await deleteDocument(documentId)
        // Remove from selection if selected
        if (selectedDocumentIds.includes(documentId)) {
          onSelectionChange(selectedDocumentIds.filter(id => id !== documentId))
        }
        onDocumentsUpdate()
        toast.success('Document deleted successfully')
      } catch (error) {
        console.error('Delete error:', error)
        toast.error('Failed to delete document')
      }
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  const handleUrlExtractComplete = async (document: Document) => {
    try {
      // Document is already saved by UrlInput component
      // Just refresh the documents list and keep modal open
      onDocumentsUpdate()
      toast.success(`Web content extracted: ${document.name}`)
    } catch (error) {
      console.error('Error handling URL extraction:', error)
      toast.error('Failed to process extracted content')
    }
  }

  const handleRenameDocument = (documentId: string) => {
    setRenamingDocumentId(documentId)
  }

  const handleRenameCancel = () => {
    setRenamingDocumentId(null)
  }

  const handleRenameSave = (documentId: string, newName: string) => {
    try {
      renameDocument(documentId, newName)
      setRenamingDocumentId(null)
      onDocumentsUpdate()
      toast.success('Document renamed successfully')
      return true
    } catch (error) {
      console.error('Rename error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to rename document')
      return false
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Document Management</h2>
            <p className="text-sm text-gray-600 mt-1">
              Upload files or extract content from URLs for chat context
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('files')}
            className={`flex items-center gap-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'files'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <Upload className="w-4 h-4" />
            Upload Files
          </button>
          <button
            onClick={() => setActiveTab('url')}
            className={`flex items-center gap-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'url'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <Globe className="w-4 h-4" />
            Extract from URL
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === 'files' && (
          <div className="p-6 border-b border-gray-200">
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive
                  ? 'border-blue-400 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              {isUploading ? (
                <div className="space-y-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <div className="text-sm text-gray-600">Uploading documents...</div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                  <div>
                    <p className="text-lg font-medium text-gray-900">Upload Documents</p>
                    <p className="text-sm text-gray-600 mt-1">
                      Drag and drop files here, or click to browse
                    </p>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-2 justify-center">
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Choose Files
                    </button>
                    <div className="text-xs text-gray-500 flex items-center justify-center">
                      PDF, TXT, DOC, DOCX (max 10MB each)
                    </div>
                  </div>
                </div>
              )}
            </div>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".pdf,.txt,.doc,.docx"
              onChange={(e) => handleFileSelect(e.target.files)}
              className="hidden"
            />
          </div>
        )}

        {activeTab === 'url' && (
          <div className="p-6 border-b border-gray-200">
            <UrlInput onExtractComplete={handleUrlExtractComplete} />
          </div>
        )}

        {/* Documents List */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* List Header */}
          <div className="flex items-center justify-between p-6 pb-4">
            <div className="flex items-center gap-4">
              <h3 className="font-medium text-gray-900">
                Documents ({documents.length})
              </h3>
              {documents.length > 0 && (
                <button
                  onClick={handleSelectAll}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  {selectedDocumentIds.length === documents.length ? 'Deselect All' : 'Select All'}
                </button>
              )}
            </div>
            {selectedDocumentIds.length > 0 && (
              <div className="text-sm text-gray-600">
                {selectedDocumentIds.length} selected
              </div>
            )}
          </div>

          {/* Documents Grid */}
          <div className="flex-1 overflow-y-auto px-6 pb-6">
            {documents.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No documents uploaded yet</p>
                <p className="text-sm text-gray-400 mt-1">
                  Upload some documents to get started with AI-powered chat
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {documents.map((document) => {
                  const isSelected = selectedDocumentIds.includes(document.id)
                  return (
                    <div
                      key={document.id}
                      className={`group border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md ${
                        isSelected
                          ? 'border-blue-500 bg-blue-50 shadow-sm'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleDocumentToggle(document.id)}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          <FileText className={`w-5 h-5 flex-shrink-0 ${
                            isSelected ? 'text-blue-600' : 'text-gray-500'
                          }`} />
                          <div className="min-w-0 flex-1">
                            {renamingDocumentId === document.id ? (
                              <DocumentRenameInput
                                initialName={document.name}
                                onSave={(newName) => handleRenameSave(document.id, newName)}
                                onCancel={handleRenameCancel}
                                className="w-full"
                              />
                            ) : (
                              <h4 className={`font-medium truncate ${
                                isSelected ? 'text-blue-900' : 'text-gray-900'
                              }`}>
                                {document.name}
                              </h4>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-1 ml-2">
                          {isSelected && (
                            <div className="p-1 bg-blue-600 rounded-full">
                              <Check className="w-3 h-3 text-white" />
                            </div>
                          )}
                          {renamingDocumentId !== document.id && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleRenameDocument(document.id)
                              }}
                              className="p-1 hover:bg-blue-100 rounded text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity"
                              title="Rename document"
                            >
                              <Edit2 className="w-4 h-4" />
                            </button>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDeleteDocument(document.id)
                            }}
                            className="p-1 hover:bg-red-100 rounded text-red-500 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                      
                      <div className="space-y-2 text-sm text-gray-600">
                        <div className="flex items-center justify-between">
                          <span>Size:</span>
                          <span>{formatFileSize(document.size)}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Uploaded:</span>
                          <span>{formatDate(document.uploadedAt)}</span>
                        </div>

                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            {selectedDocumentIds.length > 0 && (
              <span>
                {selectedDocumentIds.length} document{selectedDocumentIds.length !== 1 ? 's' : ''} selected for chat context
              </span>
            )}
          </div>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Done
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}