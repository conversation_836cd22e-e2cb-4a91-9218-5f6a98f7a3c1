import { Readability } from '@mozilla/readability';
import { convertHtmlToMarkdown } from 'dom-to-semantic-markdown';
import { WebExtractionResult, WebExtractionOptions } from '../types';

/**
 * Validates if a URL is properly formatted
 */
export function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * Extracts content from a URL and converts it to Markdown
 */
export async function extractWebContent(
  url: string,
  options: WebExtractionOptions = {}
): Promise<WebExtractionResult> {
  const { maxContentLength = 50000, timeout = 25000, auth } = options;

  if (!isValidUrl(url)) {
    return { success: false, url, error: 'Invalid URL format' };
  }

  try {
    const controller = new AbortController();
    setTimeout(() => controller.abort(), timeout);
    
    let proxyUrl = `/api/web-proxy?url=${encodeURIComponent(url)}&timeout=${timeout}`;
    
    // Add authentication parameters if provided
    if (auth && auth.username && auth.password) {
      proxyUrl += `&username=${encodeURIComponent(auth.username)}&password=${encodeURIComponent(auth.password)}`;
    }
    
    const response = await fetch(proxyUrl, { signal: controller.signal });
    
    if (!response.ok) {
      if (response.status === 408) {
        return { success: false, url, error: `Request timeout after ${timeout}ms. The website may be slow or unresponsive.` };
      }
      return { success: false, url, error: `Failed to fetch content (${response.status}: ${response.statusText})` };
    }

    const html = await response.text();
    const doc = new DOMParser().parseFromString(html, 'text/html');
    
    const reader = new Readability(doc);
    const article = reader.parse();
    
    if (!article) {
      return { success: false, url, error: 'Could not extract content' };
    }

    let markdownContent = convertHtmlToMarkdown(article.content || '');
    
    if (markdownContent.length > maxContentLength) {
      markdownContent = markdownContent.substring(0, maxContentLength) + '\n\n[Content truncated...]';
    }

    const excerpt = (article.textContent || '')
      .replace(/\s+/g, ' ')
      .trim()
      .substring(0, 200) + (markdownContent.length > 200 ? '...' : '');

    return {
      success: true,
      title: article.title || 'Untitled',
      content: markdownContent,
      excerpt,
      url
    };

  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return {
          success: false,
          url,
          error: `Request timeout after ${timeout}ms. The website may be slow or unresponsive.`
        };
      }
      return {
        success: false,
        url,
        error: error.message
      };
    }
    return {
      success: false,
      url,
      error: 'Extraction failed'
    };
  }
}

/**
 * Creates a web document from extracted content
 */
export function createWebDocument(extractionResult: WebExtractionResult, id?: string) {
  if (!extractionResult.success || !extractionResult.content) {
    throw new Error('Cannot create document from failed extraction');
  }

  return {
    id: id || crypto.randomUUID(),
    name: extractionResult.title || 'Web Content',
    type: 'web' as const,
    size: extractionResult.content.length,
    content: extractionResult.content,
    uploadedAt: new Date(),
    sourceUrl: extractionResult.url,
    excerpt: extractionResult.excerpt || ''
  };
}